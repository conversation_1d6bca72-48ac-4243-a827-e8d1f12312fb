<?php

namespace App\Http\Controllers\Merchant;

use App\Http\Controllers\Controller;
use App\Models\Merchant;
use App\Models\User;
use App\Services\UserService;
use App\Services\ChuanglanSmsApi;
use App\Services\MaiYaTianService;
use App\Services\QingyunService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\SystemConfig;
use App\Models\BanIp;
use Carbon\Carbon;
use Mews\Captcha\Facades\Captcha;
use Illuminate\Support\Facades\Redis;

class AuthController extends Controller
{
    /**
     * 显示登录表单
     */
    public function showLoginForm()
    {
        return view('merchant.login');
    }

    /**
     * 处理登录请求
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|max:20',
            'password' => 'required|string|min:8',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except('password'));
        }

        $credentials = $request->only('phone', 'password');
        $remember = $request->filled('remember');

        if (Auth::guard('merchant')->attempt($credentials, $remember)) {
            $request->session()->regenerate();
            return redirect()->intended(route('merchant.dashboard'));
        }

        return back()->withErrors([
            'phone' => '用户名或密码错误',
        ])->withInput($request->except('password'));
    }

    /**
     * 显示注册表单
     */
    public function showRegistrationForm()
    {
        return view('merchant.login', ['showRegisterTab' => true]);
    }

    /**
     * 生成图形验证码
     */
    public function captcha()
    {
        return Captcha::create('flat');
    }

    /**
     * 刷新图形验证码
     */
    public function refreshCaptcha()
    {
        return response()->json(['captcha' => Captcha::src('flat')]);
    }

    /**
     * 处理注册请求
     */
    public function register(Request $request, UserService $userService)
    {
        $validator = Validator::make($request->all(), [
            'shop_name' => 'required|string|max:100',
            'phone' => 'required|string|max:20|unique:merchants',
            'password' => 'required|string|min:8|confirmed',
            'province' => 'required|string|max:50',
            'city' => 'required|string|max:50',
            'district' => 'required|string|max:50',
            'city_code' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'verification_code' => 'required|string|size:4',
        ], [
            'captcha.required' => '请输入图形验证码',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except('password', 'password_confirmation', 'verification_code', 'captcha'));
        }
        $redisKey = "verification_code:register:{$request->phone}";
        if (env('APP_ENV') == 'production') {
            $storedCode = Redis::get($redisKey);
        } else {
            $storedCode = '1234';
        }

        if (!$storedCode || $storedCode !== $request->verification_code) {
            return back()
                ->withErrors(['verification_code' => '验证码无效或已过期'])
                ->withInput($request->except('password', 'password_confirmation', 'verification_code', 'captcha'));
        }

        // 验证成功后删除Redis中的验证码
        Redis::del($redisKey);

        // 开始事务以确保数据一致性
        DB::beginTransaction();

        try {
            // 创建商家账户
            $merchant = Merchant::create([
                'shop_name' => $request->shop_name,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'province' => $request->province,
                'city' => $request->city,
                'district' => $request->district,
                'city_code' => $request->city_code,
                'address' => $request->address,
                'status' => 0, // 默认状态为待审核
                'balance' => 0, // 初始余额为0
            ]);

            // 检查该手机号是否已有用户账号
            $user = User::where('phone', $request->phone)->first();

            // 如果没有用户账号，则创建一个
            if (!$user) {
                // 复用UserService的registerUser方法创建用户
                // 参数：手机号，密码，邀请码，平台信息
                $user = $userService->registerUser(
                    $request->phone,
                    $request->password,
                    '', // 空邀请码
                    SystemConfig::PlatformPT // 平台标识为跑腿平台
                );

                \Log::info("为商家创建关联用户账号成功", [
                    'merchant_id' => $merchant->id,
                    'user_id' => $user->id,
                    'phone' => $request->phone
                ]);
            }

            // 建立商家与用户的关联关系
            $merchant->user_id = $user->id;
            $merchant->save();

            DB::commit();

            // 登录商家
            Auth::guard('merchant')->login($merchant);

            // 记录登录状态，用于调试
            \Log::info('商家注册成功并尝试登录', [
                'merchant_id' => $merchant->id,
                'merchant_status' => $merchant->status,
                'authenticated' => Auth::guard('merchant')->check(),
                'auth_id' => Auth::guard('merchant')->id(),
            ]);

            return redirect()->route('merchant.dashboard')->with('success', '注册成功，请等待管理员审核');
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error("商家注册失败", [
                'error' => $e->getMessage(),
                'phone' => $request->phone
            ]);

            return redirect()->back()
                ->withErrors(['register_error' => '注册失败，请稍后重试'])
                ->withInput($request->except('password', 'password_confirmation', 'verification_code', 'captcha'));
        }
    }

    /**
     * 发送手机验证码
     */
    public function sendVerificationCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/|max:11',
            'type' => 'required|string|in:register,reset_password',
            'captcha' => 'required|captcha', // 添加图形验证码验证
        ], [
            'captcha.required' => '请输入图形验证码',
            'captcha.captcha' => '图形验证码不正确',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ]);
        }

        // 检查发送频率限制
        $phone = $request->phone;
        $rateKey = 'sms_rate_' . $phone;
        if (Cache::has($rateKey)) {
            return response()->json([
                'success' => false,
                'message' => '请求过于频繁，请稍后再试',
            ]);
        }

        // 根据类型设置Redis键名
        $type = $request->type;
        $redisKey = "verification_code:{$type}:{$phone}";

        // 生成验证码
        if (app()->environment() != 'production') {
            // 非生产环境统一使用固定验证码1234
            $code = '1234';
            $result = true;
            Log::info('非生产环境使用固定验证码：' . $code);
        } else {
            // 生产环境生成随机4位验证码
            $code = str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);

            // 调用创蓝短信API发送验证码
            $smsService = app(ChuanglanSmsApi::class);
            $msg = '【雨骑士】您的验证码是：{$var}，如非本人操作，请忽略本短信。	';
            $params = "$phone,$code";
            $result = $smsService->sendVariableSMS($msg, $params);
            Log::info('商家平台发送短信结果: ' . json_encode($result));
        }

        // 设置发送频率限制 (1分钟)
        Cache::put($rateKey, 1, now()->addMinute(1));

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => '短信发送失败，请稍后再试',
            ]);
        }

        // 使用Redis存储验证码，设置10分钟过期
        Redis::set($redisKey, $code, 'EX', 600);
        Log::info('验证码已存储到Redis', ['key' => $redisKey, 'code' => $code]);

        $response = [
            'success' => true,
            'message' => '验证码已发送',
        ];

        // 仅在非生产环境返回验证码
        if (app()->environment() != 'production') {
            $response['debug_code'] = $code;
        }

        return response()->json($response);
    }

    /**
     * 处理通过手机号重置密码请求
     */
    public function resetPasswordWithPhone(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'verification_code' => 'required|string|size:4',
            'password' => 'required|string|min:8|confirmed',
            'captcha' => 'required|captcha', // 添加图形验证码验证
        ], [
            'captcha.required' => '请输入图形验证码',
            'captcha.captcha' => '图形验证码不正确',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except('password', 'password_confirmation', 'verification_code', 'captcha'));
        }

        // 验证码验证
        $phone = $request->phone;
        $redisKey = "verification_code:reset_password:{$phone}";
        $storedCode = Redis::get($redisKey);

        // 添加调试日志
        \Log::info('验证重置密码验证码', [
            'phone' => $phone,
            'redis_key' => $redisKey,
            'stored_code' => $storedCode,
            'input_code' => $request->verification_code,
        ]);

        if (!$storedCode || $storedCode !== $request->verification_code) {
            return back()->withErrors([
                'verification_code' => '验证码无效或已过期'
            ])->withInput($request->except('password', 'password_confirmation', 'verification_code', 'captcha'));
        }

        // 验证成功后删除Redis中的验证码
        Redis::del($redisKey);

        // 查找商家并更新密码
        \Log::info("尝试查找手机号并重置密码: {$phone}");

        $merchant = Merchant::where('phone', $phone)->first();

        if (!$merchant) {
            // 仅为调试用途
            $allMerchants = Merchant::all();
            $phoneNumbers = $allMerchants->pluck('phone')->toArray();
            \Log::info("所有商家的phone字段值: " . json_encode($phoneNumbers));

            return back()->withErrors([
                'phone' => '该手机号未注册，无法重置密码'
            ])->withInput($request->except('password', 'password_confirmation', 'verification_code', 'captcha'));
        }

        \Log::info("找到商家，ID: {$merchant->id}，准备重置密码");

        $merchant->password = Hash::make($request->password);
        $merchant->setRememberToken(Str::random(60));
        $merchant->save();

        // 触发密码重置事件
        event(new PasswordReset($merchant));

        // 自动登录用户
        Auth::guard('merchant')->login($merchant);

        return redirect()->route('merchant.dashboard')->with('status', '密码已成功重置');
    }

    /**
     * 处理登出请求
     */
    public function logout(Request $request)
    {
        Auth::guard('merchant')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('merchant.login');
    }

    /**
     * 显示忘记密码表单
     */
    public function showForgotPasswordForm()
    {
        return view('merchant.forgot-password');
    }

    /**
     * 显示麦芽田授权登录表单
     */
    public function showMytLoginForm(Request $request)
    {
        // 获取从麦芽田授权回调传递过来的参数
        $code = $request->get('code');
        $redirectUri = $request->get('redirect_uri');
        $state = $request->get('state');
        $source = $request->get('source');

        // 传递这些参数到登录视图
        return view('merchant.login-maiyatian', [
            'code' => $code,
            'redirect_uri' => $redirectUri,
            'state' => $state,
            'source' => $source
        ]);
    }

    /**
     * 处理麦芽田授权登录请求
     */
    public function loginWithMyt(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|max:20',
            'password' => 'required|string|min:8',
            'code' => 'required|string',
            'redirect_uri' => 'required|string',
            'state' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except('password'));
        }

        $credentials = $request->only('phone', 'password');
        // 尝试登录
        if (Auth::guard('merchant')->attempt($credentials)) {
            $request->session()->regenerate();

            // 登录成功后，直接进行麦芽田数据绑定
            $merchant = Auth::guard('merchant')->user();

            // 记录登录成功日志
            Log::info('麦芽田授权登录成功', [
                'merchant_id' => $merchant->id,
                'phone' => $merchant->phone,
                'code' => $request->code,
                'state' => $request->state
            ]);

            try {
                // 直接调用MaiYaTianService进行授权绑定
                $client = new MaiYaTianService();
                $result = $client->accessToken(
                    $merchant->id,
                    $request->code,
                    $merchant->phone,
                    $request->state,
                    $merchant->city,
                    $merchant->city_code
                );
                // 绑定成功后重定向回麦芽田提供的redirect_uri
                return redirect(urldecode($request->redirect_uri) . "?state=" . $request->state);
            } catch (\Exception $e) {
                Log::error('麦芽田授权绑定失败', [
                    'merchant_id' => $merchant->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // 绑定失败，但登录成功，重定向到商家后台
                return redirect()->route('merchant.dashboard')->withErrors([
                    'binding_error' => '登录成功，但授权绑定失败: ' . $e->getMessage(),
                ]);
            }
        }

        return back()->withErrors([
            'phone' => '用户名或密码错误',
        ])->withInput($request->except('password'));
    }

    /**
     * 显示青云授权登录表单
     */
    public function showQingyunLoginForm(Request $request)
    {
        // 获取从青云授权回调传递过来的参数
        $shopId = $request->get('shopId');
        $shopName = $request->get('shopName');
        $deviceType = $request->get('deviceType');

        // 传递这些参数到登录视图
        return view('merchant.login-qingyun', [
            'shopId' => $shopId,
            'shopName' => $shopName,
            'deviceType' => $deviceType
        ]);
    }

    /**
     * 处理青云授权登录请求
     */
    public function loginWithQingyun(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|max:20',
            'password' => 'required|string|min:8',
            'shopId' => 'required|string',
            'shopName' => 'required|string',
            'deviceType' => 'required|string|in:app,pc',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except('password'));
        }

        $credentials = $request->only('phone', 'password');
        // 尝试登录
        if (Auth::guard('merchant')->attempt($credentials)) {
            $request->session()->regenerate();

            // 登录成功后，直接进行青云数据绑定
            $merchant = Auth::guard('merchant')->user();

            // 更新商家类型为青云
            $merchant->update(['merchant_type' => 'qingyun']);

            // 记录登录成功日志
            Log::info('青云授权登录成功', [
                'merchant_id' => $merchant->id,
                'phone' => $merchant->phone,
                'shop_id' => $request->shopId,
                'shop_name' => $request->shopName,
                'device_type' => $request->deviceType,
                'merchant_type_updated' => 'qingyun'
            ]);

            try {
                // 直接调用QingyunService进行授权绑定
                $client = new QingyunService();
                $result = $client->bindMerchant(
                    $merchant->id,
                    $merchant->phone,
                    $request->shopId,
                    $request->shopName,
                    $request->deviceType,
                    $merchant->city,
                    $merchant->city_code
                );

                // 检查回调结果
                $callbackResult = $result['data']['callback_result'] ?? null;
                $successMessage = '青云授权绑定成功';

                if ($callbackResult && !$callbackResult['success']) {
                    $successMessage .= '，但青云系统回调失败，系统将自动重试';
                    Log::error('青云授权绑定成功但回调失败', [
                        'merchant_id' => $merchant->id,
                        'shop_id' => $request->shopId,
                        'callback_result' => $callbackResult
                    ]);
                }

                // 绑定成功后重定向到商家后台
                return redirect()->route('merchant.dashboard')->with('success', $successMessage);
            } catch (\Exception $e) {
                Log::error('青云授权绑定失败', [
                    'merchant_id' => $merchant->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // 绑定失败，但登录成功，重定向到商家后台
                return redirect()->route('merchant.dashboard')->withErrors([
                    'binding_error' => '登录成功，但授权绑定失败: ' . $e->getMessage(),
                ]);
            }
        }

        return back()->withErrors([
            'phone' => '用户名或密码错误',
        ])->withInput($request->except('password'));
    }
}
