<?php

/**
 * 青云签名调试脚本
 * 用于调试青云地址询价接口的签名问题
 */

// 模拟请求参数（从你提供的请求中提取）
$requestParams = [
    "developerId" => "87ac701aa1aa429dae04c359b6af76f8",
    "timestamp" => "1754405077",
    "version" => "1.0",
    "sign" => "df5179a26a0b1f51ba6f15d0d75caefcd52ae8b2",
    "orderId" => "HB15080396238958592",
    "tradeOrderId" => "15080396259930112",
    "carrierMerchantId" => "QY_11",
    "serviceCode" => "HBT_YQS_1",
    "recipientName" => "测试同学",
    "recipientPhone" => "15312341234",
    "recipientAddress" => "浙江省杭州市余杭区西溪北苑北区108栋",
    "recipientLng" => "120040000",
    "recipientLat" => "30290000",
    "prebook" => "0",
    "expectedDeliveryTime" => "1754407446",
    "expectedPickupTime" => "0",
    "insuredMark" => "0",
    "totalValue" => "10.0",
    "totalWeight" => "1000",
    "totalVolume" => "0",
    "riderPickMethod" => "0",
    "goodsDetails" => "[{\"count\":1,\"name\":\"all for paws调试商品请不要购买110\",\"price\":10.0,\"unit\":\"份\"}]",
    "tradeOrderSource" => "300",
    "senderLng" => "120205048",
    "senderLat" => "30290126",
    "senderName" => "测试门店1002",
    "senderContract" => "0575-87639585",
    "senderAddressDetail" => "浙江省杭州市余杭区海创科技中心",
    "carModelCode" => null,
    "category" => "100",
    "extInfo" => null,
    "expectedLeftDeliveryTime" => "1754406246",
    "expectedRightDeliveryTime" => "1754407446"
];

$secret = '>]dn>q+sN=h0QjJZ6fYre7U&{[}X(YQ&t{abFqKEotU&@NCdF%oDR,IJZmCX*~<V';

echo "=== 青云签名调试 ===\n\n";

echo "1. 原始请求参数：\n";
foreach ($requestParams as $key => $value) {
    echo "  {$key} = " . (is_null($value) ? 'null' : $value) . "\n";
}

// 模拟中间件的签名生成逻辑
function generateSignature($params, $secret) {
    // 1. 将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
    $filteredParams = [];
    foreach ($params as $key => $value) {
        // 排除 sign 参数和空值参数
        if ($key === 'sign' || $value === null || $value === '') {
            continue;
        }

        // 数组和对象转为JSON字符串
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }

        // 布尔值转换
        if (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        }

        $filteredParams[$key] = $value;
    }

    // 按参数名字典顺序排序
    ksort($filteredParams);

    echo "\n2. 过滤并排序后的参数：\n";
    foreach ($filteredParams as $key => $value) {
        echo "  {$key} = {$value}\n";
    }

    // 2. 将参数以参数1值1参数2值2...的顺序拼接
    $signString = '';
    foreach ($filteredParams as $key => $value) {
        $signString .= $key . $value;
    }

    echo "\n3. 参数拼接字符串：\n";
    echo $signString . "\n";

    // 3. 按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
    $signString = $secret . $signString;

    echo "\n4. 完整签名字符串（secret + 参数）：\n";
    echo "长度: " . strlen($signString) . "\n";
    echo "前100字符: " . substr($signString, 0, 100) . "...\n";

    // 4. 对加密前的字符串进行sha1加密并转为小写字符串，得到签名
    return strtolower(sha1($signString));
}

$calculatedSign = generateSignature($requestParams, $secret);
$providedSign = $requestParams['sign'];

echo "\n5. 签名结果：\n";
echo "  计算出的签名: {$calculatedSign}\n";
echo "  提供的签名:   {$providedSign}\n";
echo "  签名匹配:     " . ($calculatedSign === $providedSign ? '是' : '否') . "\n";

if ($calculatedSign !== $providedSign) {
    echo "\n=== 签名不匹配，尝试其他可能的原因 ===\n";
    
    // 尝试1: 检查goodsDetails是否需要特殊处理
    echo "\n尝试1: 检查goodsDetails格式\n";
    $goodsDetails = $requestParams['goodsDetails'];
    echo "原始goodsDetails: {$goodsDetails}\n";
    
    // 解析并重新编码
    $parsedGoods = json_decode($goodsDetails, true);
    if ($parsedGoods) {
        $reEncodedGoods = json_encode($parsedGoods, JSON_UNESCAPED_UNICODE);
        echo "重新编码的goodsDetails: {$reEncodedGoods}\n";
        echo "是否相同: " . ($goodsDetails === $reEncodedGoods ? '是' : '否') . "\n";
    }
    
    // 尝试2: 检查是否有隐藏字符或编码问题
    echo "\n尝试2: 检查字符编码\n";
    foreach (['recipientName', 'recipientAddress', 'senderName', 'senderAddressDetail'] as $field) {
        if (isset($requestParams[$field])) {
            $value = $requestParams[$field];
            echo "{$field}: " . mb_detect_encoding($value) . " - " . strlen($value) . " bytes\n";
        }
    }
}

echo "\n=== 调试完成 ===\n";
