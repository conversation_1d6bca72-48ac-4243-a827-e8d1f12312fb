<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Middleware\VerifyQingyunSignature;

// 模拟请求数据
$requestData = [
    "developerId" => "87ac701aa1aa429dae04c359b6af76f8",
    "timestamp" => "1754405077",
    "version" => "1.0",
    "sign" => "df5179a26a0b1f51ba6f15d0d75caefcd52ae8b2",
    "orderId" => "HB15080396238958592",
    "tradeOrderId" => "15080396259930112",
    "carrierMerchantId" => "QY_11",
    "serviceCode" => "HBT_YQS_1",
    "recipientName" => "测试同学",
    "recipientPhone" => "15312341234",
    "recipientAddress" => "浙江省杭州市余杭区西溪北苑北区108栋",
    "recipientLng" => "120040000",
    "recipientLat" => "30290000",
    "prebook" => "0",
    "expectedDeliveryTime" => "1754407446",
    "expectedPickupTime" => "0",
    "insuredMark" => "0",
    "totalValue" => "10.0",
    "totalWeight" => "1000",
    "totalVolume" => "0",
    "riderPickMethod" => "0",
    "goodsDetails" => "[{\"count\":1,\"name\":\"all for paws调试商品请不要购买110\",\"price\":10.0,\"unit\":\"份\"}]",
    "tradeOrderSource" => "300",
    "senderLng" => "120205048",
    "senderLat" => "30290126",
    "senderName" => "测试门店1002",
    "senderContract" => "0575-87639585",
    "senderAddressDetail" => "浙江省杭州市余杭区海创科技中心",
    "carModelCode" => null,
    "category" => "100",
    "extInfo" => null,
    "expectedLeftDeliveryTime" => "1754406246",
    "expectedRightDeliveryTime" => "1754407446"
];

echo "=== 青云签名验证测试 ===\n\n";

// 测试1: 模拟JSON请求
echo "1. 测试JSON请求格式:\n";
$jsonData = json_encode($requestData, JSON_UNESCAPED_UNICODE);
echo "JSON数据: " . substr($jsonData, 0, 100) . "...\n";

// 测试2: 模拟表单请求
echo "\n2. 测试表单请求格式:\n";
$formData = [];
foreach ($requestData as $key => $value) {
    if ($value !== null) {
        $formData[] = $key . '=' . urlencode($value);
    }
}
$formString = implode('&', $formData);
echo "表单数据: " . substr($formString, 0, 100) . "...\n";

// 手动计算签名
function calculateSignature($params, $secret) {
    // 过滤参数
    $filteredParams = [];
    foreach ($params as $key => $value) {
        if ($key === 'sign' || $value === null || $value === '') {
            continue;
        }
        
        // 数组和对象转为JSON字符串
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        // 布尔值转换
        if (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        }
        
        $filteredParams[$key] = $value;
    }
    
    // 按参数名字典顺序排序
    ksort($filteredParams);
    
    echo "\n过滤并排序后的参数:\n";
    foreach ($filteredParams as $key => $value) {
        echo "  {$key} = {$value}\n";
    }
    
    // 拼接参数
    $signString = '';
    foreach ($filteredParams as $key => $value) {
        $signString .= $key . $value;
    }
    
    echo "\n参数拼接字符串:\n{$signString}\n";
    
    // 加上secret
    $signString = $secret . $signString;
    
    echo "\n完整签名字符串长度: " . strlen($signString) . "\n";
    echo "前100字符: " . substr($signString, 0, 100) . "...\n";
    
    // 计算签名
    return strtolower(sha1($signString));
}

$secret = '>]dn>q+sN=h0QjJZ6fYre7U&{[}X(YQ&t{abFqKEotU&@NCdF%oDR,IJZmCX*~<V';

echo "\n3. 手动计算签名:\n";
$calculatedSign = calculateSignature($requestData, $secret);
$providedSign = $requestData['sign'];

echo "\n计算出的签名: {$calculatedSign}\n";
echo "提供的签名:   {$providedSign}\n";
echo "签名匹配:     " . ($calculatedSign === $providedSign ? '是' : '否') . "\n";

// 测试不同的参数处理方式
echo "\n4. 测试不同的参数处理方式:\n";

// 方式1: 直接使用原始参数
echo "\n方式1 - 直接使用原始参数:\n";
$sign1 = calculateSignature($requestData, $secret);
echo "签名: {$sign1}\n";

// 方式2: 模拟JSON解析
echo "\n方式2 - 模拟JSON解析:\n";
$jsonParams = json_decode(json_encode($requestData, JSON_UNESCAPED_UNICODE), true);
$sign2 = calculateSignature($jsonParams, $secret);
echo "签名: {$sign2}\n";

// 方式3: 模拟表单解析
echo "\n方式3 - 模拟表单解析:\n";
parse_str($formString, $parsedForm);
$sign3 = calculateSignature($parsedForm, $secret);
echo "签名: {$sign3}\n";

echo "\n=== 测试完成 ===\n";
